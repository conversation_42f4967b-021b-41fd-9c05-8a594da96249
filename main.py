"""Entry point for the refactored Xplot application."""

import data_io
import processing
import tkinter as tk
from tkinter import messagebox
from dialogs.calculator_dialog import CalculatorDialog
from dialogs.column_select_dialog import ColumnSelectionDialog
from dialogs.plot_settings_dialog import PlotSettingsDialog
from plotting import create_plot

class XplotApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the root window initially
        self.root.protocol("WM_DELETE_WINDOW", self.quit_application)
        
    def quit_application(self):
        """Safely quit the application."""
        if messagebox.askokcancel("Quit", "Are you sure you want to quit?"):
            self.root.quit()
            self.root.destroy()
            return True
        return False

    def run(self):
        """Run the main application loop."""
        try:
            while True:
                las_files = data_io.load_multiple_las_files()
                if not las_files:
                    if messagebox.askyesno("No Files", "No LAS files were loaded. Would you like to try again?"):
                        continue
                    break

                # Calculator workflow with retry logic
                while True:
                    calc = CalculatorDialog(self.root, las_files)
                    self.root.wait_window(calc)  # Wait for the dialog to close
                    
                    if getattr(calc, 'cancelled', False):
                        if self.quit_application():
                            return
                        break
                        
                    if not calc.result:
                        # No calculations entered, proceed
                        break
                        
                    # Use the enhanced calculation function with validation
                    success = processing.execute_calculations_with_validation(las_files, calc.result)
                    if success:
                        break
                    # If not successful, the loop will continue to show calculator again

                # Column selection dialog
                col_dlg = ColumnSelectionDialog(self.root, las_files)
                self.root.wait_window(col_dlg)
                if not getattr(col_dlg, 'selection', None):
                    if self.quit_application():
                        return
                    continue

                # Plot settings dialog
                settings_dlg = PlotSettingsDialog(self.root, col_dlg.selection.get("z_col"))
                self.root.wait_window(settings_dlg)
                if not getattr(settings_dlg, 'settings', None):
                    if self.quit_application():
                        return
                    continue

                # Create the plot
                choice = create_plot(
                    las_files,
                    col_dlg.selection["x_col"],
                    col_dlg.selection["y_col"],
                    col_dlg.selection["class_col"],
                    col_dlg.selection["depth_ranges"],
                    settings_dlg.settings,
                    col_dlg.selection["z_col"],
                )

                if choice != "restart":
                    break

        except Exception as e:
            messagebox.showerror("Error", f"An unexpected error occurred: {str(e)}")
        finally:
            if tk._default_root:  # Check if Tk root still exists
                self.root.quit()
                self.root.destroy()

def main() -> None:
    """Run the Xplot application."""
    app = XplotApp()
    app.run()
    
    # Ensure all Tkinter windows are properly destroyed
    root = tk.Tk()
    root.withdraw()
    root.quit()
    root.destroy()

if __name__ == "__main__":  # pragma: no cover - manual invocation
    main()
