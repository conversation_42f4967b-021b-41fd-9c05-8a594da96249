"""Dialog for selecting depth ranges for multiple LAS wells."""

from __future__ import annotations

from typing import Dict, List, Optional, Tuple

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd

import data_io


class DepthRangeDialog(tk.Toplevel):
    """Dialog for selecting depth ranges for multiple wells."""

    def __init__(
        self,
        parent: tk.Misc,
        las_files: List,
        log_keywords: Dict[str, List[str]],
        preloaded_excel_df: Optional[pd.DataFrame] = None,
    ) -> None:
        super().__init__(parent)
        self.las_files = las_files
        self.log_keywords = log_keywords
        self.preloaded_excel_df = preloaded_excel_df
        self.result_depth_ranges: Dict[str, Tuple[float, float]] = {}
        self.title("Select Depth Ranges")
        self.geometry("600x400")
        self._build_ui()

    def _build_ui(self) -> None:
        """Create the UI widgets."""
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Select method for depth ranges:").pack(anchor="w")
        self.method_var = tk.StringVar(value="manual")
        manual_rb = ttk.Radiobutton(main_frame, text="Manual", variable=self.method_var, value="manual", command=self._on_radio_click)
        excel_rb = ttk.Radiobutton(main_frame, text="Excel", variable=self.method_var, value="excel", command=self._on_radio_click)
        manual_rb.pack(anchor="w")
        excel_rb.pack(anchor="w")

        self.container = ttk.Frame(main_frame)
        self.container.pack(fill=tk.BOTH, expand=True, pady=10)
        self.submit_btn = ttk.Button(main_frame, text="Submit", command=self._on_submit)
        self.submit_btn.pack(pady=5)
        self.submit_btn.config(state=tk.NORMAL)  # Default enabled for manual
        self._on_radio_click()

    def _on_radio_click(self) -> None:
        for child in self.container.winfo_children():
            child.destroy()
        if self.method_var.get() == "manual":
            self._create_manual_ui()
            self.submit_btn.config(state=tk.NORMAL)
        else:
            self._create_excel_ui()
            # Disable submit until Excel is loaded
            if self.preloaded_excel_df is not None:
                self.submit_btn.config(state=tk.NORMAL)
            else:
                self.submit_btn.config(state=tk.DISABLED)

    def _create_manual_ui(self) -> None:
        wells = [las.well.WELL.value for las in self.las_files]
        self.manual_entries: List[Tuple[str, tk.Entry, tk.Entry]] = []
        for i, well in enumerate(wells):
            ttk.Label(self.container, text=well).grid(row=i, column=0, padx=5, pady=5)
            top_e = ttk.Entry(self.container)
            bottom_e = ttk.Entry(self.container)
            top_e.grid(row=i, column=1, padx=5, pady=5)
            bottom_e.grid(row=i, column=2, padx=5, pady=5)
            self.manual_entries.append((well, top_e, bottom_e))

    def _create_excel_ui(self) -> None:
        ttk.Label(self.container, text="Load Excel boundaries and select depths").pack(anchor="w")
        btn = ttk.Button(self.container, text="Load Excel", command=self._on_load_excel)
        btn.pack(anchor="w")
        self.preview = tk.Text(self.container, height=10)
        self.preview.pack(fill=tk.BOTH, expand=True, pady=5)
        self._update_preview()

    def _on_load_excel(self) -> None:
        df = data_io.load_boundaries_from_excel()
        if df is not None:
            self.preloaded_excel_df = data_io.filter_excel_data_for_las_wells(df, [las.well.WELL.value for las in self.las_files])
            # Enable submit only if required columns exist
            if self.preloaded_excel_df is not None and all(col in self.preloaded_excel_df.columns for col in ["Well", "Top", "Bottom"]):
                self.submit_btn.config(state=tk.NORMAL)
            else:
                messagebox.showerror("Invalid Excel", "Excel file must have columns: Well, Top, Bottom.")
                self.submit_btn.config(state=tk.DISABLED)
        self._update_preview()

    def _update_preview(self) -> None:
        self.preview.delete("1.0", tk.END)
        if self.preloaded_excel_df is not None:
            self.preview.insert(tk.END, self.preloaded_excel_df.head().to_string())
        else:
            self.preview.insert(tk.END, "No Excel data loaded")

    def _on_submit(self) -> None:
        if self.method_var.get() == "manual":
            for well, top_e, bottom_e in self.manual_entries:
                try:
                    top = float(top_e.get())
                    bottom = float(bottom_e.get())
                except ValueError:
                    messagebox.showerror("Invalid Input", f"Invalid depths for {well}")
                    return
                self.result_depth_ranges[well] = (top, bottom)
        else:
            if self.preloaded_excel_df is None:
                messagebox.showerror("No Data", "Load Excel data first")
                return
            # Only allow submit if required columns exist
            if not all(col in self.preloaded_excel_df.columns for col in ["Well", "Top", "Bottom"]):
                messagebox.showerror("Invalid Excel", "Excel file must have columns: Well, Top, Bottom.")
                return
            for well in [las.well.WELL.value for las in self.las_files]:
                df_well = self.preloaded_excel_df[self.preloaded_excel_df["Well"] == well]
                if not df_well.empty:
                    top = float(df_well.iloc[0]["Top"])
                    bottom = float(df_well.iloc[0]["Bottom"])
                    self.result_depth_ranges[well] = (top, bottom)
        self.destroy()

    def get_selected_depth_ranges(self) -> Dict[str, Tuple[float, float]]:
        """Return the selected depth ranges."""
        return self.result_depth_ranges


def get_depth_ranges(las_files, log_keywords, preloaded_excel_df=None):
    root = tk.Tk()
    root.withdraw()
    dialog = DepthRangeDialog(root, las_files, log_keywords, preloaded_excel_df)
    root.wait_window(dialog)
    root.destroy()
    return dialog.get_selected_depth_ranges()

