"""Input/output utilities for loading LAS and Excel files."""

from __future__ import annotations

from typing import List, Optional

import lasio
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox


def load_multiple_las_files() -> Optional[List[lasio.LASFile]]:
    """Prompt the user to select and load multiple LAS files."""
    root = tk.Tk()
    root.withdraw()

    file_paths = filedialog.askopenfilenames(
        title="Select LAS files",
        filetypes=[("LAS files", "*.las")],
    )

    if not file_paths:
        print("No files selected by user.")
        return None

    las_files: List[lasio.LASFile] = []
    for path in file_paths:
        try:
            las_files.append(lasio.read(path))
        except Exception as exc:  # noqa: BLE001
            print(f"ERROR loading file {path}: {exc}")

    if not las_files:
        print("No LAS files were successfully loaded.")
        return None

    print(f"Successfully loaded {len(las_files)} LAS files")
    return las_files


def load_boundaries_from_excel(title: str = "Select Excel file with boundary information") -> Optional[pd.DataFrame]:
    """Load boundary information from an Excel file."""
    root = tk.Tk()
    root.withdraw()

    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")],
    )

    if not file_path:
        print("No Excel file selected.")
        return None

    try:
        df = pd.read_excel(file_path)
    except Exception as exc:  # noqa: BLE001
        messagebox.showerror("Error Loading File", f"An error occurred while loading the Excel file:\n{exc}")
        return None

    required_columns = ["Well", "Surface", "MD"]
    missing = [col for col in required_columns if col not in df.columns]
    if missing:
        messagebox.showerror(
            "Missing Columns",
            f"The Excel file is missing: {', '.join(missing)}",
        )
        return None

    if df.empty:
        messagebox.showerror("Empty File", "The Excel file contains no data.")
        return None

    return df


def filter_excel_data_for_las_wells(df: pd.DataFrame, las_files: List[lasio.LASFile]) -> Optional[pd.DataFrame]:
    """Return only rows from *df* that match well names in *las_files*."""
    if df is None:
        return None

    well_names = [las.well.WELL.value for las in las_files]
    filtered = df[df["Well"].isin(well_names)]

    if filtered.empty:
        print("Warning: No matching wells found in Excel file.")
        return None

    return filtered


def load_excel_depth_ranges(las_files: List[lasio.LASFile]) -> Optional[pd.DataFrame]:
    """Prompt the user for an Excel file with depth ranges and filter by wells."""
    root = tk.Tk()
    root.withdraw()

    if not messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\nThe file should have columns named 'Well', 'Surface', and 'MD'.",
    ):
        return None

    df = load_boundaries_from_excel("Select Excel file with depth ranges")
    return filter_excel_data_for_las_wells(df, las_files) if df is not None else None
